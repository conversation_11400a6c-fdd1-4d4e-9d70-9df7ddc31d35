/* Modern Authentication Theme - Green Color Scheme (#198754) */

/* Base Variables */
:root {
    --primary-color: #198754;
    --primary-light: #20c997;
    --primary-dark: #146c43;
    --primary-rgb: 25, 135, 84;
    
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-light: #a0aec0;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-light: #edf2f7;
    
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(var(--primary-rgb), 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    --transition: all 0.3s ease;
}

/* Modern Page Layout */
.modern-auth-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    position: relative;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

/* Split Layout */
.auth-split-left {
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.9), rgba(32, 201, 151, 0.9)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.auth-split-right {
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Branding Section */
.auth-branding {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.brand-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.brand-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.brand-features {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.feature-item i {
    width: 30px;
    margin-right: 1rem;
    font-size: 1.3rem;
}

/* Form Container */
.auth-form-container {
    width: 100%;
    max-width: 420px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: var(--shadow-lg);
}

.auth-icon i {
    font-size: 1.8rem;
    color: white;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: block;
}

.modern-input {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-secondary);
}

.modern-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background: var(--bg-primary);
    outline: none;
}

.modern-input.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.modern-input.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Input Groups */
.input-group-modern {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.1rem;
    transition: var(--transition);
    z-index: 2;
}

.input-group-modern .modern-input {
    padding-left: 3rem;
}

.input-group-modern .modern-input:focus + .input-icon {
    color: var(--primary-color);
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    z-index: 2;
}

.password-toggle-btn:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn-modern {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn-modern:active {
    transform: translateY(0);
}

.btn-modern.loading {
    pointer-events: none;
    opacity: 0.8;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-modern {
    display: flex;
    align-items: center;
}

.checkbox-modern input {
    margin-right: 0.5rem;
    accent-color: var(--primary-color);
}

.checkbox-modern label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
}

/* Links */
.link-modern {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.link-modern:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Dividers */
.auth-divider {
    text-align: center;
    position: relative;
    margin: 2rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.auth-divider span {
    background: var(--bg-primary);
    padding: 0 1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Info Sections */
.info-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
    margin-bottom: 2rem;
}

.info-section h6 {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.info-section h6 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.info-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-section li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.info-section li i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    width: 16px;
    flex-shrink: 0;
}

/* Alerts */
.alert-modern {
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
}

.alert-modern.alert-success {
    background: rgba(var(--primary-rgb), 0.1);
    border-left-color: var(--success-color);
    color: var(--primary-dark);
}

.alert-modern.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: #721c24;
}

.alert-modern.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #664d03;
}

/* Responsive Design */
@media (max-width: 991px) {
    .auth-split-left {
        display: none;
    }
    
    .auth-split-right {
        width: 100%;
        min-height: 100vh;
    }
    
    .auth-container {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .brand-title {
        font-size: 2rem;
    }

    .auth-form-container {
        max-width: none;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* Login Page Specific Styles */
.modern-login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
}

.login-left {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.9), rgba(32, 201, 151, 0.9)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="fish" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23fish)"/></svg>');
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-branding {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.login-right {
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(25, 135, 84, 0.3);
}

.login-icon i {
    font-size: 1.8rem;
    color: white;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: #718096;
    font-size: 1rem;
}

.password-input-group {
    position: relative;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 0.5rem;
    accent-color: #198754;
}

.forgot-password-link {
    color: #198754;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.forgot-password-link:hover {
    text-decoration: underline;
}

.btn-login {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(25, 135, 84, 0.4);
    color: white;
}

.login-divider {
    text-align: center;
    position: relative;
    margin: 2rem 0;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
}

.login-divider span {
    background: white;
    padding: 0 1rem;
    color: #a0aec0;
    font-weight: 500;
}

.register-link {
    text-align: center;
    margin-bottom: 2rem;
}

.register-btn {
    color: #198754;
    text-decoration: none;
    font-weight: 600;
}

.register-btn:hover {
    text-decoration: underline;
}

.benefits-preview {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #198754;
}

.benefits-preview h6 {
    color: #4a5568;
    margin-bottom: 1rem;
    font-weight: 600;
}

.benefits-preview ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-preview li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #718096;
    font-size: 0.9rem;
}

.benefits-preview li i {
    color: #198754;
    margin-right: 0.75rem;
    width: 16px;
}

@media (max-width: 991px) {
    .modern-login-page {
        padding: 2rem 0;
    }

    .login-right {
        min-height: 100vh;
    }
}

@media (max-width: 576px) {
    .login-right {
        padding: 1rem;
    }

    .login-form-container {
        max-width: none;
    }
}
