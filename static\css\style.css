/* Global styles */
.list-group-item.active {
    background-color: #198653 !important;
    border-color: #198653 !important;
}

.list-group-item.active:hover {
    background-color: #157347 !important;
    border-color: #157347 !important;
}

/* Navbar specific fixes */
.navbar {
    position: relative !important;
    z-index: 1030 !important;
}

.navbar .dropdown {
    position: relative !important;
}

.navbar .dropdown-menu {
    position: absolute !important;
    z-index: 1031 !important;
    display: none !important;
}

.navbar .dropdown-menu.show {
    display: block !important;
}

/* Ensure dropdown toggle is visible */
.navbar .dropdown-toggle {
    color: rgba(255, 255, 255, 0.75) !important;
}

.navbar .dropdown-toggle:hover {
    color: rgba(255, 255, 255, 1) !important;
}

/* User profile and logout styling */
.navbar .nav-item .nav-link.text-danger {
    color: #dc3545 !important;
    transition: color 0.3s ease;
}

.navbar .nav-item .nav-link.text-danger:hover {
    color: #bb2d3b !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-radius: 0.375rem;
}

/* Settings dropdown icon styling */
.navbar .dropdown-toggle::after {
    display: none !important;
}

/* Global dropdown fixes - prevent overflow issues */
body, html {
    overflow-x: visible !important;
}

.container, .container-fluid {
    overflow: visible !important;
}

.row {
    overflow: visible !important;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    overflow: visible !important;
}

/* Fix for all select dropdowns */
select, .form-select {
    position: relative !important;
    z-index: 9999 !important;
}

/* Contact form controls should not be affected by global z-index */
.contact-form-body .form-control {
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for Bootstrap dropdowns */
.dropdown-menu {
    z-index: 9999 !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15) !important;
    display: none !important;
}

.dropdown-menu.show {
    display: block !important;
}

/* Ensure dropdown containers don't clip */
.dropdown, .btn-group {
    position: relative !important;
}

/* Card and panel fixes */
.card, .panel, .card-body, .panel-body {
    overflow: visible !important;
}

/* Form group fixes */
.form-group, .mb-3, .mb-2, .mb-1 {
    overflow: visible !important;
}

/* Random Lakes Section Styles */
.random-lakes .card {
    height: 100%;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.random-lakes .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.random-lakes .card-img-top {
    height: 200px !important;
    object-fit: cover;
    width: 100%;
    flex-shrink: 0;
}

.random-lakes .card-body {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 1.5rem;
}

.random-lakes .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
    line-height: 1.3;
    min-height: 2.6rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.random-lakes .card-text {
    flex-grow: 1;
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.5;
    min-height: 3rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.random-lakes .rating-section {
    margin-bottom: 1rem;
    min-height: 2.5rem;
    display: flex;
    align-items: flex-start;
}

.random-lakes .fish-species-section {
    margin-bottom: 1rem;
    min-height: 3rem;
    display: flex;
    align-items: flex-start;
}

.random-lakes .fish-species-section .d-flex {
    min-height: 2.5rem;
    align-items: flex-start;
}

.random-lakes .badge {
    margin-bottom: 0.25rem;
}

.random-lakes .lake-info {
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.random-lakes .lake-info .list-unstyled {
    margin-bottom: 0;
}

.random-lakes .lake-info .list-unstyled li {
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.95rem;
}

.random-lakes .card-footer-btn {
    margin-top: auto;
    padding-top: 1rem;
}

.random-lakes .btn-success {
    width: 100%;
    padding: 0.75rem 1rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.random-lakes .btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
    transform: translateY(-1px);
}

/* Ensure equal spacing in grid */
.random-lakes .row {
    margin: 0 -0.75rem;
}

.random-lakes .col {
    padding: 0 0.75rem;
    margin-bottom: 1.5rem;
    display: flex;
}

/* Additional styling for better visual consistency */
.random-lakes .rating-stars i {
    font-size: 0.9rem;
    margin-right: 0.1rem;
}

.random-lakes .lake-info i {
    color: #198754;
    width: 16px;
    text-align: center;
}

.random-lakes .fish-species-section:empty {
    min-height: 1rem;
    margin-bottom: 0.5rem;
}

.random-lakes .rating-section:empty {
    min-height: 1rem;
    margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .random-lakes .card {
        min-height: 450px;
    }

    .random-lakes .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 576px) {
    .random-lakes .card {
        min-height: 400px;
    }

    .random-lakes .card-body {
        padding: 1rem;
    }

    .random-lakes .col {
        margin-bottom: 1rem;
    }
}

:root {
    --primary-color: #198653;
    --secondary-color: #188552;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

a {
    color: #198653;
    text-decoration: none;
}

a:hover {
    color: #157347;
}

.form-check-input:checked {
    background-color: #00ff88 !important;
    border-color: #198653 !important;
}

.text-success {
    color: #198653 !important;
}

.btn-success {
    background-color: #198653 !important;
    border-color: #198653 !important;
}

.btn-success:hover {
    background-color: #157347 !important;
    border-color: #157347 !important;
}

/* Rest of the existing styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navbar Styles */
.navbar {
    background-color: transparent;
    transition: all 0.3s ease;
    padding: 1rem 0;
    flex-direction: row;
}

.navbar.scrolled {
    background-color: rgb(255, 255, 255);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 1.5rem;
}

.logo-img {
    width: auto;
    height: 40px;
    margin-right: 10px;
}

.nav-link {
    color: #555 !important;
    font-weight: 600;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-toggler {
    border: 0;
    padding: 0.5rem;
    transition: all 0.3s ease;
    outline: none;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

@media (max-width: 991px) {
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    .nav-link {
        padding: 0.75rem 1rem !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .nav-item:last-child .nav-link {
        border-bottom: none;
    }

    .navbar-nav {
        margin: 0 -1rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        position: fixed !important;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030 !important;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 1rem;
    }

    .navbar-brand {
        margin: 0;
        width: auto;
        order: 1;
    }

    .navbar-collapse {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        max-height: calc(100vh - 70px);
        overflow-y: auto;
        order: 2;
        z-index: 1029;
    }

    .navbar-toggler {
        padding: 0.5rem;
        margin: 0;
        border: 1px solid rgba(0,0,0,.1);
        border-radius: 4px;
        order: 3;
        position: relative;
        transition: all 0.3s ease-in-out;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        transition: all 0.3s ease-in-out;
    }

    .navbar-toggler[aria-expanded='true'] .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M6 6L24 24M6 24L24 6'/%3e%3c/svg%3e");
        transform: rotate(180deg);
    }

    .nav-link {
        padding: 1rem !important;
        border-bottom: 1px solid rgba(0,0,0,.1);
        font-size: 1.1rem;
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    /* Mobile navbar styling */
    .navbar-nav .nav-item .nav-link {
        padding: 0.75rem 1rem !important;
        border-bottom: 1px solid rgba(0,0,0,.1) !important;
        background-color: white !important;
        color: #333 !important;
    }

    .navbar-nav .nav-item .nav-link.text-danger {
        color: #dc3545 !important;
    }

    .navbar-nav .nav-item .dropdown-toggle {
        padding: 0.75rem 1rem !important;
        border-bottom: 1px solid rgba(0,0,0,.1) !important;
        background-color: white !important;
        color: #333 !important;
    }

    .navbar-nav .nav-item .dropdown-toggle:hover {
        color: #198754 !important;
    }

    /* Mobile dropdown menu positioning */
    .navbar-nav .dropdown-menu {
        position: static !important;
        float: none !important;
        width: 100% !important;
        margin-top: 0 !important;
        background-color: #f8f9fa !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    /* Default dropdown styling for mobile navbar */
    .navbar .dropdown-menu {
        border: none;
        background: transparent;
        padding: 0;
        margin-top: 0;
        position: static !important;
        width: 100% !important;
        box-shadow: none !important;
        transform: none !important;
        inset: auto !important;
    }

    .navbar .dropdown-item {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,.1);
        white-space: normal;
        background-color: white !important;
        color: #555 !important;
        display: flex !important;
        align-items: center !important;
        text-decoration: none !important;
        font-weight: 500;
    }

    .navbar .dropdown-item:hover,
    .navbar .dropdown-item:focus {
        background-color: #198754 !important;
        color: white !important;
    }

    .navbar .dropdown-item:last-child {
        border-bottom: none;
    }

    .navbar .dropdown-item i {
        width: 20px;
        margin-right: 0.75rem;
        color: #6c757d;
        font-size: 1rem;
    }

    .navbar .dropdown-item:hover i,
    .navbar .dropdown-item:focus i {
        color: white !important;
    }

    .navbar .dropdown-divider {
        margin: 0;
        border-color: rgba(0,0,0,0.1);
        background-color: rgba(0,0,0,0.1);
        height: 1px;
    }

    body {
        padding-top: 70px;
    }

    /* Global mobile dropdown fixes */
    .form-select, select {
        position: relative !important;
        z-index: auto !important;
    }

    /* Ensure filter sections don't overlap navbar */
    .search-filter-section,
    .filter-controls,
    .search-card {
        position: relative !important;
        z-index: 1020 !important;
    }

    /* Prevent any content from overlapping navbar */
    .container > .row:first-child,
    .container > section:first-child,
    .container > div:first-child {
        margin-top: 0.5rem;
    }
}

/* AdSense Styling */
.ad-container {
    margin: 2rem auto;
    padding: 1rem;
    max-width: 100%;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    position: relative;
}

.ad-label {
    margin-bottom: 0.5rem;
    text-align: center;
}

.ad-label small {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive ad containers */
.ad-banner {
    margin: 1.5rem 0;
    text-align: center;
}

.ad-sidebar {
    margin: 1rem 0;
    position: sticky;
    top: 100px;
}

.ad-inline {
    margin: 2rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    border-left: 4px solid #198754;
}

/* Mobile ad adjustments */
@media (max-width: 768px) {
    .ad-container {
        margin: 1rem auto;
        padding: 0.75rem;
    }

    .ad-sidebar {
        position: static;
        margin: 1rem 0;
    }

    .ad-inline {
        margin: 1.5rem 0;
        padding: 0.75rem;
    }
}

/* Hide ads for users who haven't consented to cookies */
body:not(.cookies-accepted) .ad-container,
body:not(.cookies-accepted) .ad-banner,
body:not(.cookies-accepted) .ad-sidebar,
body:not(.cookies-accepted) .ad-inline {
    display: none !important;
}

/* Desktop dropdown styles - restore normal Bootstrap behavior */
@media (min-width: 992px) {
    .navbar .dropdown-menu {
        background: white !important;
        border: 1px solid rgba(0,0,0,.15) !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        padding: 0.5rem 0 !important;
        margin-top: 0.125rem !important;
        width: auto !important;
        min-width: 10rem !important;
    }

    .navbar .dropdown-item {
        padding: 0.25rem 1rem !important;
        border-bottom: none !important;
        background-color: transparent !important;
        color: #212529 !important;
        font-weight: 400 !important;
        white-space: nowrap !important;
        display: block !important;
    }

    .navbar .dropdown-item:hover,
    .navbar .dropdown-item:focus {
        background-color: #e9ecef !important;
        color: #16181b !important;
    }

    .navbar .dropdown-item i {
        color: #6c757d !important;
        margin-right: 0.5rem !important;
        width: auto !important;
    }

    .navbar .dropdown-item:hover i,
    .navbar .dropdown-item:focus i {
        color: #16181b !important;
    }

    .navbar .dropdown-divider {
        height: 0 !important;
        margin: 0.5rem 0 !important;
        overflow: hidden !important;
        border-top: 1px solid rgba(0,0,0,.15) !important;
        background-color: transparent !important;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.8;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    z-index: 2;
}

.hero-text {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    padding: 20px;
    max-width: 800px;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-text .btn-success {
    font-size: 1.2rem;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-text .btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Waves Animation */
.waves-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15vh;
    overflow: hidden;
    z-index: 4;
}

.waves {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 20px;
    max-height: 300px;
}

.parallax > use {
    animation: move-waves 25s cubic-bezier(.55, .5, .45, .5) infinite;
}

.parallax > use:nth-child(1) {
    animation-delay: -2s;
    animation-duration: 7s;
}

.parallax > use:nth-child(2) {
    animation-delay: -3s;
    animation-duration: 10s;
}

.parallax > use:nth-child(3) {
    animation-delay: -4s;
    animation-duration: 13s;
}

.parallax > use:nth-child(4) {
    animation-delay: -5s;
    animation-duration: 20s;
}

@keyframes move-waves {
    0% {
        transform: translate3d(-90px, 0, 0);
    }
    100% {
        transform: translate3d(85px, 0, 0);
    }
}

/* Welcome Section */
.welcome-section {
    padding: 5rem 0;
    background-color: white;
}

/* Welcome Section Image */
.welcome-section .image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.welcome-section .image-container img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.welcome-section .image-container::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    border-radius: 50%;
    opacity: 0.5;
}

.welcome-section .image-container img:hover {
    transform: scale(1.02);
}

.text-container {
    padding: 2rem;
}

.text-container h2 {
    color: var(--dark-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.text-container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--primary-color);
}

.text-container p {
    color: #666;
    font-size: 1.2rem;
    line-height: 1.8;
}

/* Featured Products Section */
.deals-section {
    padding: 5rem 0;
    background-color: white;
}

.deals-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.deal-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.deal-card:hover {
    transform: translateY(-10px);
}

.deal-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.deal-content {
    padding: 1.5rem;
}

.deal-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.deal-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

/* Fishing Locations Section */
.fishing-explore-section {
    background-color: var(--primary-color);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.fishing-explore-container {
    display: flex;
    align-items: center;
    gap: 4rem;
    position: relative;
    z-index: 2;
}

.fishing-text-content {
    flex: 1;
}

.fishing-text-content h2 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 2rem;
}

.fishing-text-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    opacity: 0.9;
}

.fishing-image-container {
    flex: 1;
    position: relative;
}

.fishing-image-container::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
}

.fishing-image-container img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
}

@media (max-width: 992px) {
    .fishing-text-content h2 {
        font-size: 2.5rem;
        text-align: center;
    }
    
    .fishing-text-content p {
        text-align: center;
    }
    
    .fishing-image-container::before {
        display: none;
    }
}

/* Video Tutorials Section */
.video-courses-section {
    padding: 5rem 0;
    background-color: var(--light-color);
}

.course-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.course-card:hover {
    transform: translateY(-10px);
}

.card-img-container {
    position: relative;
    overflow: hidden;
}

.card-img-container img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.badge-play {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.course-card:hover .badge-play {
    transform: scale(1.1);
}

/* Testimonials Section */
.testimonial-section {
    padding: 5rem 0;
    background-color: white;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 1rem;
}

.testimonial-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    object-fit: cover;
}

/* Promotional Video Section */
.promo-section {
    position: relative;
    padding: 5rem 0;
    background-image: url('../images/img_4.webp');
    background-size: cover;
    background-position: center;
    color: white;
}

.promo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
}

.promo-play-button {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.promo-play-button:hover {
    transform: scale(1.1);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 5rem 0 2rem;
}

.footer-logo {
    height: 40px;
    margin-bottom: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-flag {
    height: 20px;
    margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .fishing-explore-container {
        flex-direction: column;
        text-align: center;
    }

    .fishing-text-content {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        background-color: white;
    }

    .navbar-collapse {
        background-color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .deals-container {
        grid-template-columns: 1fr;
    }

    .welcome-section .image-container {
        margin-bottom: 2rem;
    }

    .text-container {
        text-align: center;
    }

    .text-container h2::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.text-success {
    color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--primary-color) !important;
}

/* Solunar Page Styles */
.solunar-card {
    transition: transform 0.2s ease;
}

.solunar-card:hover {
    transform: translateY(-5px);
}

#solunar-data .card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#solunar-data .card-body {
    padding: 1.5rem;
}

#solunar-data h5 {
    color: var(--primary-color);
    font-weight: 600;
}

#solunar-data .progress {
    border-radius: 1rem;
    background-color: #e9ecef;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

#solunar-data .progress-bar {
    border-radius: 1rem;
    transition: width 0.6s ease;
}

#date-picker, #location-select {
    border: 1px solid #ced4da;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#date-picker:focus, #location-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 134, 83, 0.25);
}

.major-period, .minor-period {
    font-family: 'Roboto Mono', monospace;
    letter-spacing: 0.5px;
}

.card-subtitle i {
    color: var(--primary-color);
}

.list-unstyled i {
    width: 20px;
    text-align: center;
}

/* Solunar Section */
.solunar-section {
    background-color: var(--light-color);
    padding: 5rem 0;
}

.solunar-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.solunar-card:hover {
    transform: translateY(-5px);
}

.moon-phase-icon {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.fishing-times {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
}

.fishing-times h6 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.fish-icons {
    margin: 1rem 0;
}

.fish-icons i {
    font-size: 1.5rem;
    margin: 0 3px;
    transition: color 0.3s ease;
}

.fish-icons i.text-primary {
    color: var(--primary-color) !important;
}

/* County Selection Section */
.county-selection-section {
    background-color: var(--light-color);
    padding: 5rem 0;
}

.county-selection-section h2 {
    color: var(--dark-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.county-selection-section p {
    color: #666;
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
}

.map-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.map-link {
    display: inline-block;
    transition: transform 0.3s ease;
}

.map-link:hover {
    transform: scale(1.02);
}

.romania-map {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@media (max-width: 768px) {
    .county-selection-section h2 {
        font-size: 2rem;
    }

    .county-selection-section p {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .map-container {
        padding: 1rem;
    }

    /* Global mobile navbar fixes */
    .navbar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1030 !important;
        width: 100% !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }

    .navbar .container {
        padding-left: 15px !important;
        padding-right: 15px !important;
        position: relative !important;
    }

    .navbar-brand {
        position: absolute !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin: 0 !important;
    }

    .navbar-toggler {
        position: relative !important;
        z-index: 1031 !important;
        margin-left: auto !important;
    }

    /* Add top padding to body to account for fixed navbar */
    body {
        padding-top: 76px !important;
    }

    /* Ensure navbar content doesn't overflow */
    .navbar-nav {
        max-width: 100% !important;
    }

    .navbar-collapse {
        margin-top: 0.5rem !important;
    }
}

/* Leaflet Map Styles */
.leaflet-container a {
    color: white !important;
}

/* Remove focus outline and selection rectangles from Leaflet map */
.leaflet-container:focus {
    outline: none !important;
}

.leaflet-container {
    outline: none !important;
}

/* Remove any selection boxes or focus rectangles */
.leaflet-control-container *:focus {
    outline: none !important;
}

.leaflet-clickable:focus {
    outline: none !important;
}

/* Remove any white/black border rectangles that appear on click */
.leaflet-zoom-box {
    display: none !important;
}

.leaflet-control-zoom a:focus {
    outline: none !important;
}

.leaflet-bar a:focus {
    outline: none !important;
}

/* Remove any selection or highlight boxes */
.leaflet-overlay-pane svg {
    pointer-events: none;
}

.leaflet-overlay-pane svg * {
    pointer-events: none;
}

/* Disable text selection on map */
.leaflet-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Remove any border or outline that might appear */
.leaflet-container *,
.leaflet-container *:before,
.leaflet-container *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    outline: none !important;
    border: none !important;
}

/* Specifically target any white/black rectangle elements */
.leaflet-container .leaflet-control-container .leaflet-control {
    outline: none !important;
}

.leaflet-container .leaflet-control-container .leaflet-control * {
    outline: none !important;
}
