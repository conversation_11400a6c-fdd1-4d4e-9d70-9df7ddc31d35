# .htaccess for Răsfățul Pescarului on Hostinger
# This file configures Apache settings for the Django application

# Enable rewrite engine
RewriteEngine On

# Force HTTPS redirect
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Force www redirect (optional - remove if you don't want www)
RewriteCond %{HTTP_HOST} ^rasfatul-pescarului\.ro [NC]
RewriteRule ^(.*)$ https://www.rasfatul-pescarului.ro/$1 [L,R=301]

# Security headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options DENY
    
    # XSS Protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Content type sniffing protection
    Header set X-Content-Type-Options nosniff
    
    # Referrer policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pagead2.googlesyndication.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:; frame-src https://www.google.com;"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Cache control headers
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.py">
    Order allow,deny
    Deny from all
</Files>

<Files "requirements.txt">
    Order allow,deny
    Deny from all
</Files>

<Files "manage.py">
    Order allow,deny
    Deny from all
</Files>

# Protect directories
<DirectoryMatch "^/.*(\.git|__pycache__|\.pytest_cache)">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# Custom error pages (create these files in your static directory)
ErrorDocument 404 /static/errors/404.html
ErrorDocument 500 /static/errors/500.html

# Prevent access to admin from unauthorized IPs (optional)
# <Location "/admin">
#     Order deny,allow
#     Deny from all
#     Allow from YOUR_IP_ADDRESS
# </Location>

# Django static files handling
RewriteCond %{REQUEST_URI} ^/static/
RewriteRule ^static/(.*)$ /static/$1 [L]

RewriteCond %{REQUEST_URI} ^/media/
RewriteRule ^media/(.*)$ /media/$1 [L]

# Django application handling (if using WSGI)
# This might need adjustment based on Hostinger's Python setup
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /passenger_wsgi.py/$1 [QSA,L]
