# Generated by Django 5.1.6 on 2025-06-16 19:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0016_contactsettings'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='lake',
            name='price_per_day',
        ),
        migrations.AddField(
            model_name='lake',
            name='price_12h',
            field=models.DecimalField(decimal_places=2, default=1, help_text='Prețul pentru 12 ore de pescuit în lei românești (ex: 30.00)', max_digits=10, verbose_name='Preț pescuit 12h*'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lake',
            name='price_24h',
            field=models.DecimalField(decimal_places=2, default=1, help_text='Prețul pentru 24 ore de pescuit în lei românești (ex: 50.00)', max_digits=10, verbose_name='Preț pescuit 24h*'),
            preserve_default=False,
        ),
    ]
