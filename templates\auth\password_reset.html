{% extends 'base.html' %}
{% load static %}

{% block title %}Recuperare parolă - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-auth-theme.css' %}">
{% endblock %}

{% block content %}
<div class="modern-auth-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex auth-split-left">
                <div class="auth-branding">
                    <div class="brand-content">
                        <h1 class="brand-title">
                            <i class="fas fa-key me-3"></i>
                            Recuperare Parolă
                        </h1>
                        <p class="brand-subtitle">Îți vom trimite instrucțiuni pentru resetarea parolei</p>
                        <div class="brand-features">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Proces securizat de resetare</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-envelope"></i>
                                <span>Link trimis pe email</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-clock"></i>
                                <span>Link valid 24 de ore</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-lock"></i>
                                <span>Parolă nouă sigură</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Password Reset Form -->
            <div class="col-lg-6 auth-split-right">
                <div class="auth-form-container">
                    <div class="auth-header">
                        <div class="auth-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h2 class="auth-title">Ai uitat parola?</h2>
                        <p class="auth-subtitle">Introdu adresa de email pentru a primi instrucțiuni de resetare</p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert-modern alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate class="password-reset-form">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Adresa de email
                            </label>
                            <input type="email" 
                                   name="{{ form.email.name }}" 
                                   id="{{ form.email.id_for_label }}"
                                   class="modern-input" 
                                   placeholder="Introdu adresa de email asociată contului"
                                   value="{{ form.email.value|default:'' }}"
                                   required>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert-modern alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <button type="submit" class="btn-modern w-100">
                            <i class="fas fa-paper-plane me-2"></i>Trimite instrucțiuni
                        </button>
                    </form>

                    <div class="info-section">
                        <h6><i class="fas fa-info-circle"></i>Cum funcționează:</h6>
                        <ul>
                            <li><i class="fas fa-check-circle"></i>Introduci adresa de email</li>
                            <li><i class="fas fa-check-circle"></i>Primești un email cu link de resetare</li>
                            <li><i class="fas fa-check-circle"></i>Accesezi link-ul din email</li>
                            <li><i class="fas fa-check-circle"></i>Setezi o parolă nouă</li>
                        </ul>
                    </div>

                    <div class="auth-divider">
                        <span>sau</span>
                    </div>

                    <div class="register-link text-center">
                        <p>Îți amintești parola? 
                            <a href="{% url 'main:autentificare' %}" class="link-modern">
                                <i class="fas fa-sign-in-alt me-1"></i>Autentifică-te aici
                            </a>
                        </p>
                        <p>Nu ai cont încă? 
                            <a href="{% url 'main:inregistrare' %}" class="link-modern">
                                <i class="fas fa-user-plus me-1"></i>Înregistrează-te aici
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.password-reset-form');
    const emailInput = document.querySelector('.modern-input');
    const submitBtn = document.querySelector('.btn-modern');

    // Enhanced input focus effects
    emailInput.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });
    
    emailInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        const emailValue = emailInput.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        // Remove previous validation classes
        emailInput.classList.remove('is-invalid', 'is-valid');
        
        if (!emailValue) {
            emailInput.classList.add('is-invalid');
            e.preventDefault();
            return;
        }
        
        if (!emailRegex.test(emailValue)) {
            emailInput.classList.add('is-invalid');
            e.preventDefault();
            return;
        }
        
        emailInput.classList.add('is-valid');

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se trimite...';
        submitBtn.disabled = true;
    });

    // Real-time email validation
    emailInput.addEventListener('input', function() {
        const emailValue = this.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        this.classList.remove('is-invalid', 'is-valid');
        
        if (emailValue && emailRegex.test(emailValue)) {
            this.classList.add('is-valid');
        } else if (emailValue) {
            this.classList.add('is-invalid');
        }
    });

    // Auto-focus email input
    emailInput.focus();
});
</script>
{% endblock %}
