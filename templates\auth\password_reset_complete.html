{% extends 'base.html' %}
{% load static %}

{% block title %}<PERSON><PERSON><PERSON> resetată - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-auth-theme.css' %}">
{% endblock %}

{% block content %}
<div class="modern-auth-page">
    <div class="auth-container">
        <div class="auth-form-container" style="max-width: 500px;">
            <div class="auth-header">
                <div class="auth-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="auth-title">Parolă resetată cu succes!</h2>
                <p class="auth-subtitle">Parola ta a fost actualizată</p>
            </div>

            <div class="info-section">
                <h6><i class="fas fa-check-circle"></i>Resetare completă:</h6>
                <ul>
                    <li><i class="fas fa-check-circle"></i>Parola ta a fost schimbată cu succes</li>
                    <li><i class="fas fa-check-circle"></i>Contul tău este acum securizat</li>
                    <li><i class="fas fa-check-circle"></i>Poți să te autentifici cu noua parolă</li>
                    <li><i class="fas fa-check-circle"></i>Toate sesiunile anterioare au fost închise</li>
                </ul>
            </div>

            <div class="alert-modern alert-success">
                <i class="fas fa-shield-alt me-2"></i>
                <strong>Securitate:</strong> Pentru siguranța maximă, îți recomandăm să nu folosești 
                această parolă pe alte site-uri și să o schimbi periodic.
            </div>

            <div class="d-grid gap-2">
                <a href="{% url 'main:autentificare' %}" class="btn-modern">
                    <i class="fas fa-sign-in-alt me-2"></i>Autentifică-te acum
                </a>
            </div>

            <div class="auth-divider">
                <span>sau</span>
            </div>

            <div class="register-link text-center">
                <p>Înapoi la pagina principală? 
                    <a href="{% url 'main:home' %}" class="link-modern">
                        <i class="fas fa-home me-1"></i>Acasă
                    </a>
                </p>
                <p>Ai nevoie de ajutor? 
                    <a href="{% url 'main:contact' %}" class="link-modern">
                        <i class="fas fa-envelope me-1"></i>Contactează-ne
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.auth-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem 1rem;
}

.alert-modern.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
    color: #155724;
}

@media (max-width: 576px) {
    .auth-form-container {
        max-width: none !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-redirect to login after 10 seconds
    let countdown = 10;
    const loginBtn = document.querySelector('.btn-modern');
    const originalText = loginBtn.innerHTML;
    
    function updateCountdown() {
        if (countdown > 0) {
            loginBtn.innerHTML = `<i class="fas fa-clock me-2"></i>Autentificare automată în ${countdown}s`;
            countdown--;
            setTimeout(updateCountdown, 1000);
        } else {
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Redirecționare...';
            setTimeout(() => {
                window.location.href = loginBtn.href;
            }, 1000);
        }
    }
    
    // Start countdown after 3 seconds
    setTimeout(() => {
        updateCountdown();
    }, 3000);
    
    // Stop countdown if user hovers over button
    loginBtn.addEventListener('mouseenter', function() {
        countdown = 0;
        this.innerHTML = originalText;
    });
});
</script>
{% endblock %}
