{% extends 'base.html' %}
{% load static %}

{% block title %}Autentificare - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-auth-theme.css' %}">
{% endblock %}

{% block content %}
<div class="modern-login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left side - Image/Branding -->
            <div class="col-lg-6 d-none d-lg-flex login-left">
                <div class="login-branding">
                    <div class="brand-content">
                        <h1 class="brand-title">
                            <i class="fas fa-fish me-3"></i>
                            Răsfățul Pescarului
                        </h1>
                        <p class="brand-subtitle">Comunitatea pescarilor din România</p>
                        <div class="brand-features">
                            <div class="feature-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Peste 1000 de locații de pescuit</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-users"></i>
                                <span>Comunitate activă de pescari</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-star"></i>
                                <span>Recenzii și evaluări reale</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="login-form-container">
                    <div class="login-header">
                        <div class="login-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h2 class="login-title">Bine ai revenit!</h2>
                        <p class="login-subtitle">Conectează-te la contul tău</p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate class="login-form">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>Nume utilizator sau email
                            </label>
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="form-control modern-input"
                                   placeholder="Introdu numele de utilizator sau email-ul"
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>Parola
                            </label>
                            <div class="password-input-group">
                                <input type="password"
                                       name="{{ form.password.name }}"
                                       id="{{ form.password.id_for_label }}"
                                       class="form-control modern-input"
                                       placeholder="Introdu parola"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <div class="form-options">
                            <div class="remember-me">
                                <input type="checkbox" id="remember-me" name="remember_me" class="form-check-input">
                                <label for="remember-me" class="form-check-label">Ține-mă minte</label>
                            </div>
                            <a href="{% url 'main:recuperare_parola' %}" class="forgot-password-link">Ai uitat parola?</a>
                        </div>

                        <button type="submit" class="btn btn-login w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>Conectează-te
                        </button>
                    </form>

                    <div class="login-divider">
                        <span>sau</span>
                    </div>

                    <div class="register-link">
                        <p>Nu ai cont încă?
                            <a href="{% url 'main:inregistrare' %}" class="register-btn">
                                Înregistrează-te aici
                            </a>
                        </p>
                    </div>

                    <div class="benefits-preview">
                        <h6><i class="fas fa-star me-2"></i>Beneficiile contului:</h6>
                        <ul>
                            <li><i class="fas fa-plus-circle"></i>Adaugă propriile balți</li>
                            <li><i class="fas fa-images"></i>Încarcă fotografii</li>
                            <li><i class="fas fa-user-circle"></i>Profil personalizat</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script src="{% static 'js/login.js' %}"></script>
{% endblock %}


