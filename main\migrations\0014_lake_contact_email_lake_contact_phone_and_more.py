# Generated by Django 5.1.6 on 2025-06-12 09:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0013_remove_lakephoto_constraints'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='lake',
            name='contact_email',
            field=models.EmailField(default=1, help_text='Adresa de email pentru contact', max_length=254, verbose_name='Email*'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lake',
            name='contact_phone',
            field=models.CharField(default=1, help_text='Numărul de telefon pentru contact (ex: 0700 123 456)', max_length=20, verbose_name='Telefon*'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lake',
            name='depth_average',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Adâncimea medie a lacului în metri', max_digits=5, null=True, verbose_name='Adâncime medie (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='depth_max',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Adâncimea maximă a lacului în metri', max_digits=5, null=True, verbose_name='Adâncime maximă (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='depth_min',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Adâncimea minimă a lacului în metri', max_digits=5, null=True, verbose_name='Adâncime minimă (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='facebook_url',
            field=models.URLField(blank=True, help_text='Pagina de Facebook a bălții (opțional)', verbose_name='Facebook'),
        ),
        migrations.AddField(
            model_name='lake',
            name='instagram_url',
            field=models.URLField(blank=True, help_text='Pagina de Instagram a bălții (opțional)', verbose_name='Instagram'),
        ),
        migrations.AddField(
            model_name='lake',
            name='length_max',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Lungimea maximă a lacului în metri', max_digits=8, null=True, verbose_name='Lungime maximă (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='length_min',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Lungimea minimă a lacului în metri', max_digits=8, null=True, verbose_name='Lungime minimă (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='number_of_stands',
            field=models.PositiveIntegerField(blank=True, help_text='Numărul total de standuri de pescuit disponibile', null=True, verbose_name='Număr de standuri'),
        ),
        migrations.AddField(
            model_name='lake',
            name='owner',
            field=models.ForeignKey(default=1, help_text='Utilizatorul care a creat și gestionează această baltă', on_delete=django.db.models.deletion.CASCADE, related_name='owned_lakes', to=settings.AUTH_USER_MODEL, verbose_name='Proprietar'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lake',
            name='surface_area',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Suprafața lacului în hectare (ex: 2.5)', max_digits=10, null=True, verbose_name='Suprafață (ha)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='website',
            field=models.URLField(blank=True, help_text='Site-ul web oficial al bălții (opțional)', verbose_name='Site web'),
        ),
        migrations.AddField(
            model_name='lake',
            name='width_max',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Lățimea maximă a lacului în metri', max_digits=8, null=True, verbose_name='Lățime maximă (m)'),
        ),
        migrations.AddField(
            model_name='lake',
            name='width_min',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Lățimea minimă a lacului în metri', max_digits=8, null=True, verbose_name='Lățime minimă (m)'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='address',
            field=models.CharField(help_text='Adresa completă a lacului (ex: Comuna X, Județul Y)', max_length=255, verbose_name='Localitate, județ*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='county',
            field=models.ForeignKey(help_text='Județul în care se află lacul', on_delete=django.db.models.deletion.CASCADE, related_name='lakes', to='main.county', verbose_name='Județul*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='facilities',
            field=models.ManyToManyField(help_text='Selectează facilitățile disponibile la acest lac', to='main.facility', verbose_name='Facilități*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='fish_species',
            field=models.ManyToManyField(help_text='Selectează speciile de pești disponibile în acest lac', to='main.fishspecies', verbose_name='Specii de pești*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='name',
            field=models.CharField(help_text='Numele complet al lacului sau bălții de pescuit', max_length=200, verbose_name='Numele bălții*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='price_per_day',
            field=models.DecimalField(decimal_places=2, help_text='Prețul pentru o zi de pescuit în lei românești (ex: 50.00)', max_digits=10, verbose_name='Prețuri/taxe de pescuit*'),
        ),
        migrations.AlterField(
            model_name='lake',
            name='rules',
            field=models.TextField(help_text='Regulile și restricțiile pentru pescuitul pe acest lac (ex: Permis obligatoriu, Se permite pescuitul din barcă, Program: 06:00-22:00)', verbose_name='Regulament*'),
        ),
        migrations.AlterField(
            model_name='lakephoto',
            name='description',
            field=models.TextField(blank=True, help_text='Descriere detaliată a imaginii (generat automat)', verbose_name='Descriere imagine'),
        ),
        migrations.AlterField(
            model_name='lakephoto',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Ordinea de afișare în galerie (generat automat)', verbose_name='Ordine'),
        ),
        migrations.AlterField(
            model_name='lakephoto',
            name='title',
            field=models.CharField(blank=True, help_text='Titlu descriptiv pentru imagine (generat automat)', max_length=200, verbose_name='Titlu imagine'),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, help_text='Numărul dvs. de telefon pentru contact', max_length=20, verbose_name='Număr de telefon')),
                ('city', models.CharField(blank=True, help_text='Orașul în care locuiți', max_length=100, verbose_name='Orașul')),
                ('bio', models.TextField(blank=True, help_text='Scurtă descriere despre dvs. și experiența la pescuit', max_length=500, verbose_name='Despre mine')),
                ('avatar', models.ImageField(blank=True, help_text='Fotografia dvs. de profil (opțional)', null=True, upload_to='profiles/avatars/', verbose_name='Fotografie de profil')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data înregistrării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
                ('county', models.ForeignKey(blank=True, help_text='Județul în care locuiți', null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.county', verbose_name='Județul')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='Utilizator')),
            ],
            options={
                'verbose_name': 'Profil utilizator',
                'verbose_name_plural': 'Profile utilizatori',
            },
        ),
    ]
