# Generated by Django 5.1.6 on 2025-06-13 10:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0015_contactmessage'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(default='Răsfățul Pescarului', help_text='Numele companiei care apare pe pagina de contact', max_length=100, verbose_name='Numele companiei')),
                ('address', models.TextField(default='Strada Exemplu, Nr. 123, București, România', help_text='Adresa fizică completă a companiei', verbose_name='Adresa completă')),
                ('phone', models.CharField(default='+40 ***********', help_text='Numărul principal de telefon pentru contact', max_length=20, verbose_name='Număr de telefon')),
                ('email', models.EmailField(default='<EMAIL>', help_text='Adresa principală de email pentru contact', max_length=254, verbose_name='Email de contact')),
                ('monday_friday_hours', models.CharField(default='09:00 – 18:00', help_text='Programul de lucru pentru zilele de luni până vineri (ex: 09:00 – 18:00)', max_length=50, verbose_name='Program Luni - Vineri')),
                ('saturday_hours', models.CharField(default='10:00 – 14:00', help_text='Programul de lucru pentru sâmbătă (ex: 10:00 – 14:00)', max_length=50, verbose_name='Program Sâmbătă')),
                ('sunday_hours', models.CharField(default='Închis', help_text='Programul de lucru pentru duminică (ex: Închis sau 10:00 – 16:00)', max_length=50, verbose_name='Program Duminică')),
                ('description', models.TextField(blank=True, help_text='Descriere suplimentară care apare pe pagina de contact', verbose_name='Descriere')),
                ('map_embed_code', models.TextField(blank=True, help_text='Codul iframe pentru harta Google Maps (opțional)', verbose_name='Cod embed hartă')),
                ('facebook_url', models.URLField(blank=True, help_text='URL-ul către pagina de Facebook', verbose_name='Link Facebook')),
                ('instagram_url', models.URLField(blank=True, help_text='URL-ul către pagina de Instagram', verbose_name='Link Instagram')),
                ('youtube_url', models.URLField(blank=True, help_text='URL-ul către canalul de YouTube', verbose_name='Link YouTube')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
            ],
            options={
                'verbose_name': 'Setări Contact',
                'verbose_name_plural': 'Setări Contact',
            },
        ),
    ]
