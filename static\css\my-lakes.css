/* My Lakes Page Styles */

.my-lakes-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border-left: 4px solid #198754;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #198754;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-weight: 500;
}

.lake-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.lake-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.lake-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    position: relative;
}

.lake-status {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: #198754;
    color: white;
}

.status-inactive {
    background: #dc3545;
    color: white;
}

.lake-content {
    padding: 1.5rem;
}

.lake-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.lake-location {
    color: #6c757d;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.lake-location i {
    margin-right: 0.5rem;
    color: #198754;
}

.lake-description {
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.lake-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.lake-meta-item {
    text-align: center;
    flex: 1;
}

.lake-meta-item i {
    display: block;
    font-size: 1.2rem;
    color: #198754;
    margin-bottom: 0.25rem;
}

.lake-meta-value {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.lake-meta-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.lake-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-lake-action {
    flex: 1;
    min-width: 120px;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-lake-action i {
    margin-right: 0.5rem;
}

.btn-lake-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-filters {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.filter-input:focus {
    border-color: #198754;
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.1);
    outline: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    margin-bottom: 1rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.lake-item {
    animation: fadeIn 0.3s ease;
}

@media (max-width: 768px) {
    .lake-actions {
        flex-direction: column;
    }
    
    .btn-lake-action {
        min-width: auto;
    }
    
    .lake-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .lake-meta-item {
        flex: none;
    }
    
    .search-filters {
        padding: 1rem;
    }
}

/* Dropdown fixes for my-lakes page */
.filter-section, .filter-controls {
    overflow: visible !important;
    position: relative !important;
    z-index: 9999 !important;
}

.filter-controls select, .form-select {
    position: relative !important;
    z-index: 9999 !important;
}

.filter-controls .form-select option {
    background: white !important;
    color: #333 !important;
    padding: 0.5rem !important;
}

.search-filters {
    overflow: visible !important;
}

.search-filters .row {
    overflow: visible !important;
}

.search-filters .col-md-6, .search-filters .col-md-4 {
    overflow: visible !important;
    position: relative !important;
}
