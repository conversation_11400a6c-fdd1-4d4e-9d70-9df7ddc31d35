{% extends 'base.html' %}
{% load static %}

{% block title %}Setează parolă nouă - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-auth-theme.css' %}">
{% endblock %}

{% block content %}
<div class="modern-auth-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex auth-split-left">
                <div class="auth-branding">
                    <div class="brand-content">
                        <h1 class="brand-title">
                            <i class="fas fa-lock me-3"></i>
                            Parolă Nouă
                        </h1>
                        <p class="brand-subtitle">Setează o parolă sigură pentru contul tău</p>
                        <div class="brand-features">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Minimum 8 caractere</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Combinație de litere și cifre</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-eye-slash"></i>
                                <span>Evită informații personale</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-key"></i>
                                <span>Unică și sigură</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side - Password Reset Form -->
            <div class="col-lg-6 auth-split-right">
                <div class="auth-form-container">
                    <div class="auth-header">
                        <div class="auth-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h2 class="auth-title">Setează parolă nouă</h2>
                        <p class="auth-subtitle">Introdu noua parolă pentru contul tău</p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert-modern alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate class="password-confirm-form">
                        {% csrf_token %}
                        
                        <div class="info-section">
                            <h6><i class="fas fa-shield-alt"></i>Cerințe parolă:</h6>
                            <ul>
                                <li><i class="fas fa-check-circle"></i>Minimum 8 caractere</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate fi similară cu informațiile personale</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate fi o parolă comună</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate conține doar cifre</li>
                            </ul>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>Parolă nouă
                            </label>
                            <div class="input-group-modern">
                                <input type="password" 
                                       name="{{ form.new_password1.name }}" 
                                       id="{{ form.new_password1.id_for_label }}"
                                       class="modern-input" 
                                       placeholder="Introdu noua parolă"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('{{ form.new_password1.id_for_label }}', 'toggleIcon1')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            {% if form.new_password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password1.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirmă parola nouă
                            </label>
                            <div class="input-group-modern">
                                <input type="password" 
                                       name="{{ form.new_password2.name }}" 
                                       id="{{ form.new_password2.id_for_label }}"
                                       class="modern-input" 
                                       placeholder="Confirmă noua parolă"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('{{ form.new_password2.id_for_label }}', 'toggleIcon2')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                            {% if form.new_password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.new_password2.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert-modern alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <button type="submit" class="btn-modern w-100">
                            <i class="fas fa-save me-2"></i>Setează parola nouă
                        </button>
                    </form>

                    <div class="auth-divider">
                        <span>sau</span>
                    </div>

                    <div class="register-link text-center">
                        <p>Îți amintești parola? 
                            <a href="{% url 'main:autentificare' %}" class="link-modern">
                                <i class="fas fa-sign-in-alt me-1"></i>Autentifică-te aici
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password toggle functionality
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.password-confirm-form');
    const inputs = document.querySelectorAll('.modern-input');
    const submitBtn = document.querySelector('.btn-modern');

    // Enhanced input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        // Password match validation
        const password1 = document.getElementById('{{ form.new_password1.id_for_label }}');
        const password2 = document.getElementById('{{ form.new_password2.id_for_label }}');
        if (password1.value !== password2.value) {
            password2.classList.add('is-invalid');
            password2.classList.remove('is-valid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            // Shake animation for invalid form
            form.classList.add('shake');
            setTimeout(() => form.classList.remove('shake'), 500);
            return;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se setează parola...';
        submitBtn.disabled = true;
    });

    // Real-time password match validation
    const password1 = document.getElementById('{{ form.new_password1.id_for_label }}');
    const password2 = document.getElementById('{{ form.new_password2.id_for_label }}');
    
    function validatePasswordMatch() {
        if (password1.value && password2.value) {
            if (password1.value === password2.value) {
                password2.classList.remove('is-invalid');
                password2.classList.add('is-valid');
            } else {
                password2.classList.add('is-invalid');
                password2.classList.remove('is-valid');
            }
        }
    }
    
    password1.addEventListener('input', validatePasswordMatch);
    password2.addEventListener('input', validatePasswordMatch);

    // Auto-focus first input
    if (password1) {
        password1.focus();
    }

    // Add shake animation CSS
    const style = document.createElement('style');
    style.textContent = `
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
