# Environment Configuration for Răsfățul Pescarului
# Copy this file to .env and fill in your actual values

# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here-generate-a-new-one-for-production

# Site Configuration
SITE_URL=https://rasfatul-pescarului.ro

# Database Configuration (MySQL for Hostinger)
DB_NAME=rasfatul_pescarului
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=3306

# Email Configuration (Hostinger SMTP)
EMAIL_HOST=smtp.hostinger.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password

# Security Settings (automatically applied when DEBUG=False)
# SECURE_SSL_REDIRECT=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# Optional: Google Analytics
# GOOGLE_ANALYTICS_ID=your-ga-id

# Optional: Social Media
# FACEBOOK_URL=https://facebook.com/your-page
# INSTAGRAM_URL=https://instagram.com/your-page

# File Upload Settings
# MAX_UPLOAD_SIZE=5242880  # 5MB in bytes
