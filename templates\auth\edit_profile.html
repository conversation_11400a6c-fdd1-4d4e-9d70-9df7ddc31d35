{% extends 'base.html' %}
{% load static %}

{% block title %}Editează profilul - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.edit-profile-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.form-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-title {
    color: #198754;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus, .form-select:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn-save {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 135, 84, 0.3);
}

.avatar-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #198754;
    margin-bottom: 1rem;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #f8f9fa;
    border: 4px solid #198754;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: #6c757d;
    font-size: 2rem;
}

.required-field {
    color: #dc3545;
}

@media (max-width: 768px) {
    .form-section {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="edit-profile-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1><i class="fas fa-user-edit me-2"></i>Editează profilul</h1>
                <p class="mb-0">Actualizează informațiile tale personale</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <form method="post" enctype="multipart/form-data" novalidate>
        {% csrf_token %}
        
        <!-- Personal Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-user me-2"></i>Informații personale
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.first_name }}
                        <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                        {% if form.first_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.last_name }}
                        <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                        {% if form.last_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="form-floating mb-3">
                {{ form.email }}
                <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                {% if form.email.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.email.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Contact & Location -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-map-marker-alt me-2"></i>Contact și locație
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.phone }}
                        <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                        {% if form.phone.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.phone.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.city }}
                        <label for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                        {% if form.city.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.city.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="form-floating mb-3">
                {{ form.county }}
                <label for="{{ form.county.id_for_label }}">{{ form.county.label }}</label>
                {% if form.county.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.county.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Bio & Avatar -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-user-circle me-2"></i>Profil și avatar
            </h3>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="{{ form.bio.id_for_label }}" class="form-label">{{ form.bio.label }}</label>
                        {{ form.bio }}
                        {% if form.bio.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.bio.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="mb-3">
                        <label class="form-label">Avatar actual</label>
                        <div>
                            {% if form.instance.avatar %}
                                <img src="{{ form.instance.avatar.url }}" alt="Avatar" class="avatar-preview" id="avatar-preview">
                            {% else %}
                                <div class="avatar-placeholder" id="avatar-preview">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.avatar.id_for_label }}" class="form-label">{{ form.avatar.label }}</label>
                        {{ form.avatar }}
                        {% if form.avatar.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.avatar.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Format recomandat: JPG, PNG (max 2MB)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-success btn-save btn-lg">
                <i class="fas fa-save me-2"></i>Salvează modificările
            </button>
            <a href="{% url 'main:utilizator_profil' %}" class="btn btn-outline-secondary btn-lg ms-3">
                <i class="fas fa-times me-2"></i>Anulează
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating label animation
    const inputs = document.querySelectorAll('.form-floating input, .form-floating select, .form-floating textarea');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('filled');
        }
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
    });

    // Avatar preview
    const avatarInput = document.getElementById('{{ form.avatar.id_for_label }}');
    const avatarPreview = document.getElementById('avatar-preview');
    
    if (avatarInput) {
        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (avatarPreview.tagName === 'IMG') {
                        avatarPreview.src = e.target.result;
                    } else {
                        // Replace placeholder with image
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = 'Avatar';
                        img.className = 'avatar-preview';
                        img.id = 'avatar-preview';
                        avatarPreview.parentNode.replaceChild(img, avatarPreview);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            // Scroll to first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });

    // Phone number formatting
    const phoneInput = document.getElementById('{{ form.phone.id_for_label }}');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Remove non-digits
            let value = e.target.value.replace(/\D/g, '');
            
            // Format Romanian phone number
            if (value.length > 0) {
                if (value.startsWith('40')) {
                    // International format
                    value = '+' + value;
                } else if (value.startsWith('0')) {
                    // National format
                    if (value.length > 4) {
                        value = value.substring(0, 4) + ' ' + value.substring(4);
                    }
                    if (value.length > 8) {
                        value = value.substring(0, 8) + ' ' + value.substring(8);
                    }
                }
            }
            
            e.target.value = value;
        });
    }
});
</script>
{% endblock %}
