/* Modern Tutorials Page Styles */

.tutorials-hero {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    color: white;
    padding: 4rem 0 2rem;
    margin-top: 76px;
    position: relative;
    overflow: hidden;
}

.tutorials-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="video-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><polygon points="20,15 20,35 35,25" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23video-pattern)"/></svg>');
    opacity: 0.3;
}

.tutorials-hero .container {
    position: relative;
    z-index: 2;
}

.tutorials-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.tutorials-hero .lead {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.tutorials-content {
    padding: 4rem 0;
    background: #f8f9fa;
}

.sidebar-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: none;
    margin-bottom: 2rem;
    overflow: hidden;
}

.sidebar-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.sidebar-header h5 {
    margin: 0;
    font-weight: 600;
}

.category-list {
    padding: 0;
}

.category-item {
    border: none;
    padding: 1rem 1.5rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-item:hover {
    background: #f8f9fa;
    color: #198754;
    transform: translateX(5px);
}

.category-item.active {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
}

.category-item.active:hover {
    color: white;
}

.category-badge {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.category-item.active .category-badge {
    background: rgba(255,255,255,0.2);
    color: white;
}

.video-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.video-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-card:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.video-card:hover .play-button {
    transform: scale(1);
}

.video-card-body {
    padding: 1.5rem;
}

.video-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #198754;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.video-date {
    color: #6c757d;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
}

.video-date i {
    margin-right: 0.5rem;
}

.btn-watch {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    border-radius: 12px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-watch:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 135, 84, 0.4);
    color: white;
}

.featured-video {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.featured-video:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.featured-thumbnail {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.featured-thumbnail img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.featured-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #198754;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.featured-date {
    color: #6c757d;
    font-size: 0.8rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
}

.empty-state-icon i {
    color: white;
    font-size: 2rem;
}

.empty-state h3 {
    color: #198754;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutorials-hero h1 {
        font-size: 2.5rem;
    }
    
    .tutorials-hero .lead {
        font-size: 1.1rem;
    }
    
    .video-card-body {
        padding: 1.25rem;
    }
    
    .video-footer {
        padding: 1rem 1.25rem;
    }
    
    .sidebar-card {
        margin-bottom: 1.5rem;
    }
    
    .video-thumbnail img {
        height: 180px;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.video-card {
    animation: fadeInUp 0.6s ease-out;
}

.video-card:nth-child(1) { animation-delay: 0.1s; }
.video-card:nth-child(2) { animation-delay: 0.2s; }
.video-card:nth-child(3) { animation-delay: 0.3s; }
.video-card:nth-child(4) { animation-delay: 0.4s; }
.video-card:nth-child(5) { animation-delay: 0.5s; }
.video-card:nth-child(6) { animation-delay: 0.6s; }

/* Video Detail Page Styles */
.video-detail-content {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.breadcrumb-modern {
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.breadcrumb-modern .breadcrumb {
    margin: 0;
}

.breadcrumb-modern .breadcrumb-item a {
    color: #198754;
    text-decoration: none;
}

.breadcrumb-modern .breadcrumb-item a:hover {
    color: #20c997;
}

.video-player-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    margin-bottom: 2rem;
}

.video-player-wrapper {
    position: relative;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
}

.video-player-wrapper iframe {
    border-radius: 20px 20px 0 0;
}

.video-info {
    padding: 2rem;
}

.video-title-main {
    color: #198754;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.video-meta-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.95rem;
}

.meta-item i {
    margin-right: 0.5rem;
    color: #198754;
}

.video-description-content {
    color: #495057;
    font-size: 1rem;
    line-height: 1.7;
}

.related-videos-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    height: fit-content;
}

.related-videos-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.related-videos-header h5 {
    margin: 0;
    font-weight: 600;
}

.related-videos-list {
    padding: 1.5rem;
}

.related-video-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.related-video-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.related-video-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: transform 0.3s ease;
}

.related-video-link:hover {
    transform: translateX(5px);
    color: inherit;
}

.related-thumbnail {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.related-thumbnail img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.related-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-video-link:hover .related-play-overlay {
    opacity: 1;
}

.related-play-button {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.related-video-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #198754;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-video-date {
    color: #6c757d;
    font-size: 0.8rem;
}

.no-related-videos {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

/* Responsive adjustments for detail page */
@media (max-width: 768px) {
    .video-title-main {
        font-size: 1.5rem;
    }

    .video-info {
        padding: 1.5rem;
    }

    .video-meta-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .related-videos-list {
        padding: 1.25rem;
    }
}
