{% extends 'base.html' %}
{% load static %}

{% block title %}Înregistrare - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-auth-theme.css' %}">
{% endblock %}

{% block content %}
<div class="modern-auth-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex auth-split-left">
                <div class="auth-branding">
                    <div class="brand-content">
                        <h1 class="brand-title">
                            <i class="fas fa-user-plus me-3"></i>
                            Alătură-te Comunității
                        </h1>
                        <p class="brand-subtitle">Descoperă cele mai bune locuri de pescuit din România</p>
                        <div class="brand-features">
                            <div class="feature-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Adaugă propriile locații de pescuit</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-camera"></i>
                                <span>Partajează fotografii și experiențe</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-users"></i>
                                <span>Conectează-te cu alți pescari</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-star"></i>
                                <span>Evaluează și recomandă locații</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right side - Registration Form -->
            <div class="col-lg-6 auth-split-right">
                <div class="auth-form-container">
                    <div class="auth-header">
                        <div class="auth-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h2 class="auth-title">Creează cont nou</h2>
                        <p class="auth-subtitle">Completează formularul pentru a te alătura comunității</p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert-modern alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate class="register-form">
                        {% csrf_token %}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>{{ form.first_name.label }}
                                    </label>
                                    <input type="text"
                                           name="{{ form.first_name.name }}"
                                           id="{{ form.first_name.id_for_label }}"
                                           class="modern-input"
                                           placeholder="Introdu prenumele"
                                           value="{{ form.first_name.value|default:'' }}"
                                           required>
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>{{ form.last_name.label }}
                                    </label>
                                    <input type="text"
                                           name="{{ form.last_name.name }}"
                                           id="{{ form.last_name.id_for_label }}"
                                           class="modern-input"
                                           placeholder="Introdu numele de familie"
                                           value="{{ form.last_name.value|default:'' }}"
                                           required>
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-at me-2"></i>{{ form.username.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="modern-input"
                                   placeholder="Alege un nume de utilizator unic"
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
                            </label>
                            <input type="email"
                                   name="{{ form.email.name }}"
                                   id="{{ form.email.id_for_label }}"
                                   class="modern-input"
                                   placeholder="Introdu adresa de email"
                                   value="{{ form.email.value|default:'' }}"
                                   required>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="info-section">
                            <h6><i class="fas fa-shield-alt"></i>Cerințe parolă:</h6>
                            <ul>
                                <li><i class="fas fa-check-circle"></i>Minimum 8 caractere</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate fi similară cu informațiile personale</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate fi o parolă comună</li>
                                <li><i class="fas fa-check-circle"></i>Nu poate conține doar cifre</li>
                            </ul>
                        </div>

                        <div class="form-group">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
                            </label>
                            <div class="input-group-modern">
                                <input type="password"
                                       name="{{ form.password1.name }}"
                                       id="{{ form.password1.id_for_label }}"
                                       class="modern-input"
                                       placeholder="Introdu o parolă sigură"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('{{ form.password1.id_for_label }}', 'toggleIcon1')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password1.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
                            </label>
                            <div class="input-group-modern">
                                <input type="password"
                                       name="{{ form.password2.name }}"
                                       id="{{ form.password2.id_for_label }}"
                                       class="modern-input"
                                       placeholder="Confirmă parola"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword('{{ form.password2.id_for_label }}', 'toggleIcon2')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert-modern alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <button type="submit" class="btn-modern w-100">
                            <i class="fas fa-user-plus me-2"></i>Creează contul
                        </button>
                    </form>

                    <div class="auth-divider">
                        <span>sau</span>
                    </div>

                    <div class="register-link text-center">
                        <p>Ai deja cont?
                            <a href="{% url 'main:autentificare' %}" class="link-modern">
                                <i class="fas fa-sign-in-alt me-1"></i>Autentifică-te aici
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password toggle functionality
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.register-form');
    const inputs = document.querySelectorAll('.modern-input');
    const submitBtn = document.querySelector('.btn-modern');

    // Enhanced input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // Real-time form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        // Email validation
        const emailInput = document.getElementById('{{ form.email.id_for_label }}');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailInput.value && !emailRegex.test(emailInput.value)) {
            emailInput.classList.add('is-invalid');
            emailInput.classList.remove('is-valid');
            isValid = false;
        }

        // Password match validation
        const password1 = document.getElementById('{{ form.password1.id_for_label }}');
        const password2 = document.getElementById('{{ form.password2.id_for_label }}');
        if (password1.value !== password2.value) {
            password2.classList.add('is-invalid');
            password2.classList.remove('is-valid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            // Shake animation for invalid form
            form.classList.add('shake');
            setTimeout(() => form.classList.remove('shake'), 500);
            return;
        }

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se creează contul...';
        submitBtn.disabled = true;
    });

    // Real-time password match validation
    const password1 = document.getElementById('{{ form.password1.id_for_label }}');
    const password2 = document.getElementById('{{ form.password2.id_for_label }}');

    function validatePasswordMatch() {
        if (password1.value && password2.value) {
            if (password1.value === password2.value) {
                password2.classList.remove('is-invalid');
                password2.classList.add('is-valid');
            } else {
                password2.classList.add('is-invalid');
                password2.classList.remove('is-valid');
            }
        }
    }

    password1.addEventListener('input', validatePasswordMatch);
    password2.addEventListener('input', validatePasswordMatch);

    // Auto-focus first empty input
    const firstEmptyInput = Array.from(inputs).find(input => !input.value.trim());
    if (firstEmptyInput) {
        firstEmptyInput.focus();
    }

    // Add shake animation CSS
    const style = document.createElement('style');
    style.textContent = `
        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
