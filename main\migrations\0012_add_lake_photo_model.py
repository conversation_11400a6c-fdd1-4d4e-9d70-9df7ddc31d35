# Generated by Django 5.1.6 on 2025-06-11 20:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0011_auto_20250605_2315'),
    ]

    operations = [
        migrations.CreateModel(
            name='LakePhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Imaginea pentru galeria lacului (format recomandat: JPG, PNG, max 2MB)', upload_to='lakes/gallery/', verbose_name='Imagine')),
                ('title', models.CharField(blank=True, help_text='Titlu descriptiv pentru imagine (opțional)', max_length=200, verbose_name='Titlu imagine')),
                ('description', models.TextField(blank=True, help_text='Descriere detaliată a imaginii (opțional)', verbose_name='<PERSON><PERSON>rier<PERSON> imagine')),
                ('is_main', models.BooleanField(default=False, help_text='Bifează pentru a seta această imagine ca principală în galerie', verbose_name='Imagine principală')),
                ('order', models.PositiveIntegerField(default=0, help_text='Ordinea de afișare în galerie (0 = prima)', verbose_name='Ordine')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
                ('lake', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='main.lake', verbose_name='Lac')),
            ],
            options={
                'verbose_name': 'Fotografie lac',
                'verbose_name_plural': 'Fotografii lac',
                'ordering': ['order', 'created_at'],
                'unique_together': {('lake', 'order')},
            },
        ),
    ]
