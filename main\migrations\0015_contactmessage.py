# Generated by Django 5.1.6 on 2025-06-13 09:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0014_lake_contact_email_lake_contact_phone_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Numele complet al persoanei care trimite mesajul', max_length=100, verbose_name='Nume complet')),
                ('email', models.EmailField(help_text='Adresa de email pentru răspuns', max_length=254, verbose_name='Email')),
                ('subject', models.CharField(help_text='Subiectul mesajului', max_length=200, verbose_name='Subiect')),
                ('message', models.TextField(help_text='Conținutul mesajului', verbose_name='<PERSON>j')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='Adresa IP de la care a fost trimis mesajul', null=True, verbose_name='Adresa IP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data trimiterii')),
                ('is_read', models.BooleanField(default=False, help_text='Marchează dacă mesajul a fost citit', verbose_name='Citit')),
                ('is_replied', models.BooleanField(default=False, help_text='Marchează dacă s-a trimis un răspuns', verbose_name='Răspuns trimis')),
                ('admin_notes', models.TextField(blank=True, help_text='Note interne pentru administratori', verbose_name='Note administrative')),
            ],
            options={
                'verbose_name': 'Mesaj de contact',
                'verbose_name_plural': 'Mesaje de contact',
                'ordering': ['-created_at'],
            },
        ),
    ]
