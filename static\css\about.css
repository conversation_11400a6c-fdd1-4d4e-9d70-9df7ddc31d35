/* Modern About Page Styles */

.about-hero {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    color: white;
    padding: 5rem 0 3rem;
    margin-top: 76px;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.about-hero .container {
    position: relative;
    z-index: 2;
}

.about-hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.about-hero .lead {
    font-size: 1.4rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.about-content {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    margin-bottom: 3rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 80px rgba(0,0,0,0.15);
}

.mission-section {
    padding: 3rem;
}

.mission-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.mission-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mission-image:hover img {
    transform: scale(1.05);
}

.mission-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.8), rgba(32, 201, 151, 0.6));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mission-image:hover .mission-overlay {
    opacity: 1;
}

.mission-overlay-text {
    color: white;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.mission-content h2 {
    color: #198754;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.mission-content .lead {
    color: #198754;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: #e9ecef;
    transform: translateX(10px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.feature-icon i {
    color: white;
    font-size: 1.2rem;
}

.feature-text {
    font-size: 1.1rem;
    color: #495057;
    margin: 0;
}

.values-section {
    padding: 4rem 0;
}

.values-header {
    text-align: center;
    margin-bottom: 4rem;
}

.values-header h2 {
    color: #198754;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.values-header p {
    font-size: 1.2rem;
    color: #6c757d;
}

.value-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    border: none;
    padding: 2.5rem;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #198754, #20c997);
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: transform 0.3s ease;
}

.value-card:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
}

.value-icon i {
    color: white;
    font-size: 2rem;
}

.value-card h4 {
    color: #198754;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.value-card p {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

.stats-section {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 4rem 0;
    margin: 4rem 0;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.stats-section .container {
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    padding: 4rem 3rem;
    text-align: center;
    margin-top: 4rem;
}

.cta-section h2 {
    color: #198754;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-section .lead {
    color: #6c757d;
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
}

.btn-cta {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    border-radius: 12px;
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-cta:hover::before {
    left: 100%;
}

.btn-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(25, 135, 84, 0.4);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .about-hero h1 {
        font-size: 2.5rem;
    }
    
    .about-hero .lead {
        font-size: 1.2rem;
    }
    
    .mission-section {
        padding: 2rem;
    }
    
    .mission-content h2 {
        font-size: 2rem;
    }
    
    .value-card {
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stats-section {
        margin: 2rem 0;
        padding: 3rem 0;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .cta-section {
        padding: 3rem 2rem;
    }
}

/* Animation for elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-card,
.value-card,
.stat-item {
    animation: fadeInUp 0.6s ease-out;
}

.value-card:nth-child(1) { animation-delay: 0.1s; }
.value-card:nth-child(2) { animation-delay: 0.2s; }
.value-card:nth-child(3) { animation-delay: 0.3s; }

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }
