{% extends 'base.html' %}
{% load static %}

{% block title %}Tutoriale - Răsfățul Pescarului{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/tutorials.css' %}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="tutorials-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1><i class="fas fa-play-circle me-3"></i>Tutoriale</h1>
                <p class="lead">Învață tehnici de pescuit și sfaturi utile de la experți pentru a deveni un pescar mai bun</p>
            </div>
        </div>
    </div>
</section>

<!-- Tutorials Content -->
<section class="tutorials-content">
    <div class="container">

        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <!-- Categories -->
                <div class="sidebar-card">
                    <div class="sidebar-header">
                        <h5><i class="fas fa-folder me-2"></i>Categorii</h5>
                    </div>
                    <div class="category-list">
                        <a href="{% url 'main:tutorials' %}"
                           class="category-item {% if not category %}active{% endif %}">
                            <span><i class="fas fa-video me-2"></i>Toate tutorialele</span>
                            <span class="category-badge">{{ videos.paginator.count|default:0 }}</span>
                        </a>
                        {% for cat in categories %}
                        <a href="{% url 'main:tutorials' %}?category={{ cat.slug }}"
                           class="category-item {% if category.id == cat.id %}active{% endif %}">
                            <span><i class="fas fa-tag me-2"></i>{{ cat.name }}</span>
                            <span class="category-badge">{{ cat.video_count|default:0 }}</span>
                        </a>
                        {% endfor %}
                    </div>
                </div>

                <!-- Featured Videos -->
                {% if featured_videos %}
                <div class="sidebar-card">
                    <div class="sidebar-header">
                        <h5><i class="fas fa-star me-2"></i>Tutoriale recomandate</h5>
                    </div>
                    <div style="padding: 1.5rem;">
                        {% for video in featured_videos %}
                        <div class="featured-video">
                            <a href="{% url 'main:video_detail' video.id %}" class="text-decoration-none">
                                <div class="featured-thumbnail">
                                    {% if video.get_thumbnail_url %}
                                    <img src="{{ video.get_thumbnail_url }}" alt="{{ video.title }}">
                                    {% else %}
                                    <img src="{% static 'images/video-placeholder.svg' %}" alt="{{ video.title }}">
                                    {% endif %}
                                    <div class="play-overlay">
                                        <div class="play-button">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                <h6 class="featured-title">{{ video.title }}</h6>
                            </a>
                            <div class="featured-date">
                                <i class="fas fa-calendar-alt me-1"></i>{{ video.created_at|date:"d.m.Y" }}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Videos Grid -->
            <div class="col-lg-9">
                {% if videos %}
                <div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-4">
                    {% for video in videos %}
                    <div class="col">
                        <div class="video-card">
                            <a href="{% url 'main:video_detail' video.id %}" class="text-decoration-none">
                                <div class="video-thumbnail">
                                    {% if video.get_thumbnail_url %}
                                    <img src="{{ video.get_thumbnail_url }}" alt="{{ video.title }}">
                                    {% else %}
                                    <img src="{% static 'images/video-placeholder.svg' %}" alt="{{ video.title }}">
                                    {% endif %}
                                    <div class="play-overlay">
                                        <div class="play-button">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            <div class="video-card-body">
                                <h5 class="video-title">{{ video.title }}</h5>
                                <p class="video-description">{{ video.description|truncatechars:120 }}</p>
                            </div>
                            <div class="video-footer">
                                <div class="video-meta">
                                    <div class="video-date">
                                        <i class="fas fa-calendar-alt"></i>{{ video.created_at|date:"d.m.Y" }}
                                    </div>
                                    <a href="{% url 'main:video_detail' video.id %}" class="btn-watch">
                                        <i class="fas fa-play me-1"></i>Urmărește
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Nu există tutoriale</h3>
                    <p>Nu am găsit niciun tutorial în această categorie. Încearcă să selectezi o altă categorie sau revino mai târziu pentru conținut nou.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}