/* Modern Contact Page Styles */

.contact-hero {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    color: white;
    padding: 4rem 0 2rem;
    margin-top: 76px;
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.contact-hero .container {
    position: relative;
    z-index: 2;
}

.contact-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.contact-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.contact-content {
    padding: 4rem 0;
    background: #f8f9fa;
}

.contact-form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 80px rgba(0,0,0,0.15);
}

.contact-form-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem;
    text-align: center;
}

.contact-form-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.contact-form-body {
    padding: 2.5rem;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.form-floating {
    margin-bottom: 1.5rem;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
}

.form-floating > .form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    background: white;
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: #198754;
}

.btn-contact {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    width: 100%;
    position: relative;
    overflow: hidden;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.btn-contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-contact:hover::before {
    left: 100%;
}

.btn-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(25, 135, 84, 0.4);
}

.contact-info-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: none;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: transform 0.3s ease;
}

.contact-info-card:hover {
    transform: translateY(-3px);
}

.contact-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.contact-info-item:last-child {
    margin-bottom: 0;
}

.contact-info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-info-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-info-text h6 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: #198754;
}

.contact-info-text p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.working-hours-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
}

.working-hours-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.working-hours-header h5 {
    margin: 0;
    font-weight: 600;
}

.working-hours-body {
    padding: 2rem;
}

.working-hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.working-hours-item:hover {
    background: #e9ecef;
}

.working-hours-item:last-child {
    margin-bottom: 0;
}

.working-hours-day {
    font-weight: 600;
    color: #495057;
}

.working-hours-time {
    color: #198754;
    font-weight: 500;
}

.alert-modern {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d1eddb, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Social Media Links */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #198754, #20c997);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(25, 135, 84, 0.4);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero h1 {
        font-size: 2.5rem;
    }
    
    .contact-hero p {
        font-size: 1.1rem;
    }
    
    .contact-form-body {
        padding: 2rem 1.5rem;
    }
    
    .contact-info-card {
        padding: 1.5rem;
    }
    
    .working-hours-body {
        padding: 1.5rem;
    }
}

/* Animation for form elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.contact-form-card,
.contact-info-card,
.working-hours-card {
    animation: fadeInUp 0.6s ease-out;
}

.contact-form-card {
    animation-delay: 0.1s;
}

.contact-info-card {
    animation-delay: 0.2s;
}

.working-hours-card {
    animation-delay: 0.3s;
}
